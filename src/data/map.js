import AMapLoader from '@amap/amap-jsapi-loader'

// 高德地图配置 - 照搬1.4版本配置并适配2.0
const MAP_CONFIG = {
  key: '51302baed58ef2d9c48e713230aee1a4', // 高德地图API Key
  version: '2.0', // 使用2.0版本，按照升级指南要求
  plugins: [
    'AMap.Scale',
    'AMap.ToolBar',
    'AMap.ControlBar',
    'AMap.Geolocation',
    'AMap.TileLayer.Satellite', // 卫星图层插件
    'AMap.TileLayer.RoadNet', // 路网图层插件
    'AMap.GeoJSON', // GeoJSON插件 - 2.0版本需要显式加载
    'AMap.HeatMap' // 热力图插件
  ],
  // 2.0版本配置
  AMapUI: {
    version: '1.1',
    plugins: []
  }
}

// 地图图层配置
const MAP_LAYER_CONFIG = {
  // 普通地图样式
  normal: {
    mapStyle: 'amap://styles/normal',
    description: '普通地图'
  },
  // 卫星地图样式
  satellite: {
    mapStyle: 'amap://styles/satellite',
    description: '卫星地图'
  },
  // 其他可用样式
  fresh: {
    mapStyle: 'amap://styles/fresh',
    description: '清新蓝'
  },
  grey: {
    mapStyle: 'amap://styles/grey',
    description: '月光银'
  },
  graffiti: {
    mapStyle: 'amap://styles/graffiti',
    description: '涂鸦'
  },
  macaron: {
    mapStyle: 'amap://styles/macaron',
    description: '马卡龙'
  },
  blue: {
    mapStyle: 'amap://styles/blue',
    description: '靛青蓝'
  },
  dark: {
    mapStyle: 'amap://styles/dark',
    description: '幻影黑'
  },
  wine: {
    mapStyle: 'amap://styles/wine',
    description: '酒庄'
  }
}

/**
 * 加载高德地图API
 * @returns {Promise<Object>} 返回AMap对象
 */
export const loadAMap = () => {
  return AMapLoader.load(MAP_CONFIG)
}

/**
 * 创建地图实例
 * @param {string} container - 地图容器ID
 * @param {Object} options - 地图配置选项
 * @returns {Promise<Object>} 返回地图实例
 */
export const createMap = async (container, options = {}) => {
  try {
    const AMap = await loadAMap()

    // 高德地图API 2.1Beta加载完成
    console.log('高德地图API 2.1Beta加载完成')
    console.log('AMap版本:', AMap.version)

    // 默认地图配置
    const defaultOptions = {
      zoom: 7, // 初始缩放级别
      center: [101.5, 25.0], // 云南省中心坐标
      mapStyle: 'amap://styles/normal', // 地图样式
      viewMode: '2D', // 默认2D视图
      lang: 'zh_cn', // 中文
      zooms: [3, 15], // 限制缩放范围，避免过度缩放
      dragEnable: true, // 允许拖拽
      zoomEnable: true, // 允许缩放
      doubleClickZoom: true, // 双击缩放
      keyboardEnable: false, // 禁用键盘操作
      scrollWheel: true, // 滚轮缩放
      touchZoom: true, // 触摸缩放
      resizeEnable: true, // 自适应容器大小
      rotateEnable: false, // 初始禁用旋转（3D模式时会启用）
      pitchEnable: false, // 初始禁用倾斜（3D模式时会启用）
      buildingAnimation: true, // 楼块出现动画
      expandZoomRange: true, // 扩展缩放范围
      jogEnable: false, // 禁用惯性拖拽
      animateEnable: true, // 动画效果
      // 3D相关配置
      pitch: 0, // 初始倾斜角度
      rotation: 0, // 初始旋转角度
      skyColor: '#87CEEB', // 3D模式天空颜色
      features: ['bg', 'road', 'building', 'point'] // 显示要素
    }

    // 合并配置
    const mapOptions = { ...defaultOptions, ...options }

    // 创建地图实例
    const map = new AMap.Map(container, mapOptions)

    // 等待地图完全加载
    await new Promise((resolve) => {
      map.on('complete', () => {
        console.log('地图加载完成')
        resolve()
      })
    })

    return { AMap, map }
  } catch (error) {
    console.error('地图加载失败:', error)
    throw error
  }
}

/**
 * 切换地图图层
 * @param {Object} map - 地图实例
 * @param {Object} AMap - AMap对象
 * @param {string} layerType - 图层类型 (normal | satellite)
 * @returns {Promise<Object>} 返回切换结果和图层实例
 */
export const switchMapLayer = async (map, AMap, layerType = 'normal') => {
  try {
    if (!map || !AMap) {
      throw new Error('地图实例或AMap对象不存在')
    }

    console.log(`切换地图图层到: ${layerType === 'satellite' ? '卫星地图' : '普通地图'}`)

    // 移除现有的卫星图层和路网图层
    const layers = map.getLayers()
    layers.forEach(layer => {
      if (layer.CLASS_NAME === 'AMap.TileLayer.Satellite' ||
          layer.CLASS_NAME === 'AMap.TileLayer.RoadNet') {
        map.remove(layer)
      }
    })

    let satelliteLayer = null
    let roadNetLayer = null

    if (layerType === 'satellite') {
      // 创建卫星图层
      satelliteLayer = new AMap.TileLayer.Satellite({
        zIndex: 1
      })

      // 创建路网图层
      roadNetLayer = new AMap.TileLayer.RoadNet({
        zIndex: 2
      })

      // 添加图层到地图
      map.add([satelliteLayer, roadNetLayer])

      console.log('卫星地图和路网图层添加成功')
    } else {
      // 普通地图模式，确保使用标准地图样式
      map.setMapStyle('amap://styles/normal')
      console.log('切换到普通地图模式')
    }

    console.log(`地图图层切换成功: ${layerType}`)
    return {
      success: true,
      layerType,
      satelliteLayer,
      roadNetLayer
    }

  } catch (error) {
    console.error('地图图层切换失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取可用的地图图层列表
 * @returns {Array} 返回图层配置数组
 */
export const getAvailableLayers = () => {
  return Object.keys(MAP_LAYER_CONFIG).map(key => ({
    key,
    ...MAP_LAYER_CONFIG[key]
  }))
}

/**
 * 加载GeoJSON数据并添加到地图 - 兼容2.1Beta版本
 * @param {Object} map - 地图实例
 * @param {Object} AMap - AMap对象
 * @param {Object} geoJsonData - GeoJSON数据
 * @param {Object} style - 样式配置
 * @returns {Object} 返回图层对象
 */
export const addGeoJsonLayer = (map, AMap, geoJsonData, style = {}) => {
  try {
    console.log('开始加载GeoJSON图层 (2.1Beta版本)')
    console.log('AMap.GeoJSON可用性:', !!AMap.GeoJSON)

    // 默认样式
    const defaultStyle = {
      strokeColor: '#0078ff', // 边界颜色
      strokeWeight: 2, // 边界宽度
      strokeOpacity: 0.8, // 边界透明度
      fillColor: '#80d8ff', // 填充颜色
      fillOpacity: 0.2, // 填充透明度
      strokeStyle: 'solid' // 边界样式
    }

    // 合并样式
    const layerStyle = { ...defaultStyle, ...style }

    // 检查GeoJSON插件是否可用
    if (!AMap.GeoJSON) {
      throw new Error('AMap.GeoJSON插件未加载，请确保在plugins中包含了AMap.GeoJSON')
    }

    // 创建GeoJSON图层 - 2.1Beta版本
    const geoJsonLayer = new AMap.GeoJSON({
      geoJSON: geoJsonData,
      getPolygon: (geojson, lnglats) => {
        return new AMap.Polygon({
          path: lnglats,
          ...layerStyle
        })
      }
    })

    // 添加到地图
    map.add(geoJsonLayer)

    // 自适应显示 - 2.1Beta版本需要传入数组
    try {
      map.setFitView([geoJsonLayer])
    } catch (fitViewError) {
      console.warn('setFitView失败，使用备用方案:', fitViewError)
      // 备用方案：设置固定的中心点和缩放级别
      map.setCenter([101.5, 25.0]) // 云南省中心
      map.setZoom(6)
    }

    console.log('GeoJSON图层加载成功 (2.1Beta版本)')
    return geoJsonLayer
  } catch (error) {
    console.error('GeoJSON图层添加失败:', error)

    // 如果GeoJSON插件不可用，尝试备用方案
    if (error.message.includes('AMap.GeoJSON')) {
      console.warn('尝试使用备用方案加载边界数据...')
      return createPolygonFromGeoJSON(map, AMap, geoJsonData, style)
    }

    throw error
  }
}

/**
 * 备用方案：直接使用Polygon创建边界
 * @param {Object} map - 地图实例
 * @param {Object} AMap - AMap对象
 * @param {Object} geoJsonData - GeoJSON数据
 * @param {Object} style - 样式配置
 * @returns {Object} 返回多边形对象
 */
const createPolygonFromGeoJSON = (map, AMap, geoJsonData, style = {}) => {
  try {
    console.log('使用备用方案创建边界多边形')

    const defaultStyle = {
      strokeColor: '#0078ff',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#80d8ff',
      fillOpacity: 0.2,
      strokeStyle: 'solid'
    }

    const layerStyle = { ...defaultStyle, ...style }

    // 提取GeoJSON中的坐标数据
    const coordinates = []
    if (geoJsonData.features && geoJsonData.features.length > 0) {
      geoJsonData.features.forEach(feature => {
        if (feature.geometry && feature.geometry.coordinates) {
          if (feature.geometry.type === 'Polygon') {
            // 处理Polygon类型
            feature.geometry.coordinates[0].forEach(coord => {
              coordinates.push([coord[0], coord[1]])
            })
          } else if (feature.geometry.type === 'MultiPolygon') {
            // 处理MultiPolygon类型
            feature.geometry.coordinates.forEach(polygon => {
              polygon[0].forEach(coord => {
                coordinates.push([coord[0], coord[1]])
              })
            })
          }
        }
      })
    }

    if (coordinates.length === 0) {
      throw new Error('无法从GeoJSON数据中提取坐标')
    }

    // 创建多边形
    const polygon = new AMap.Polygon({
      path: coordinates,
      ...layerStyle
    })

    // 添加到地图
    map.add(polygon)

    // 自适应显示 - 2.1Beta版本需要传入数组
    try {
      map.setFitView([polygon])
    } catch (fitViewError) {
      console.warn('setFitView失败，使用备用方案:', fitViewError)
      // 备用方案：设置固定的中心点和缩放级别
      map.setCenter([101.5, 25.0]) // 云南省中心
      map.setZoom(6)
    }

    console.log('备用方案创建边界成功，坐标点数:', coordinates.length)
    return polygon

  } catch (error) {
    console.error('备用方案创建边界失败:', error)
    throw error
  }
}