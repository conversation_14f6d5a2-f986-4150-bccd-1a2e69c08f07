# 云南省区县级经纬度数据

## 概述

本模块提供了云南省16个地级行政区下属的所有区县的经纬度信息，包含完整的数据结构和便捷的查询函数。

## 数据结构

### 主要数据格式

```javascript
{
  // 市州代码（行政区划代码）
  '530100': {
    cityName: '昆明市',
    cityCenter: [102.712251, 25.040609], // 市中心坐标 [经度, 纬度]
    counties: [
      {
        name: '五华区',           // 区县名称
        adcode: '530102',        // 行政区划代码
        center: [102.704412, 25.042165],    // 中心点坐标
        centroid: [102.668802, 25.164859],  // 质心坐标
        level: 'district'        // 行政级别
      }
      // ... 更多区县
    ]
  }
  // ... 更多市州
}
```

## 包含的市州

云南省16个地级行政区：

1. **昆明市** (530100) - 14个区县
2. **曲靖市** (530300) - 9个区县  
3. **玉溪市** (530400) - 9个区县
4. **保山市** (530500) - 5个区县
5. **昭通市** (530600) - 11个区县
6. **丽江市** (530700) - 5个区县
7. **普洱市** (530800) - 10个区县
8. **临沧市** (530900) - 8个区县
9. **楚雄彝族自治州** (532300) - 10个区县
10. **红河哈尼族彝族自治州** (532500) - 13个区县
11. **文山壮族苗族自治州** (532600) - 8个区县
12. **西双版纳傣族自治州** (532800) - 3个区县
13. **大理白族自治州** (532900) - 12个区县
14. **德宏傣族景颇族自治州** (533100) - 5个区县
15. **怒江傈僳族自治州** (533300) - 4个区县
16. **迪庆藏族自治州** (533400) - 3个区县

**总计：129个区县**

## API 函数

### 基础查询函数

#### `getCountiesByCity(cityCode)`
根据市州代码获取区县数据
```javascript
import { getCountiesByCity } from './yunnanCountiesComplete.js'

const kunmingData = getCountiesByCity('530100')
console.log(kunmingData.cityName) // '昆明市'
console.log(kunmingData.counties.length) // 14
```

#### `getCountyByCode(countyCode)`
根据区县代码获取区县信息
```javascript
import { getCountyByCode } from './yunnanCountiesComplete.js'

const county = getCountyByCode('530102')
console.log(county.name) // '五华区'
console.log(county.cityName) // '昆明市'
```

#### `searchCountiesByName(countyName)`
根据区县名称搜索区县信息
```javascript
import { searchCountiesByName } from './yunnanCountiesComplete.js'

const results = searchCountiesByName('区')
console.log(results.length) // 所有包含"区"字的区县数量
```

### 数据获取函数

#### `getAllCounties()`
获取所有区县的列表
```javascript
import { getAllCounties } from './yunnanCountiesComplete.js'

const allCounties = getAllCounties()
console.log(allCounties.length) // 129
```

#### `getAllCities()`
获取所有市州的列表
```javascript
import { getAllCities } from './yunnanCountiesComplete.js'

const cities = getAllCities()
console.log(cities.length) // 16
```

### 地理查询函数

#### `getCountiesInBounds(bounds)`
根据经纬度范围查找区县
```javascript
import { getCountiesInBounds } from './yunnanCountiesComplete.js'

const bounds = {
  minLng: 102.0,
  maxLng: 103.5,
  minLat: 24.5,
  maxLat: 26.0
}
const countiesInBounds = getCountiesInBounds(bounds)
```

#### `findNearestCounties(coordinates, limit)`
根据坐标查找最近的区县
```javascript
import { findNearestCounties } from './yunnanCountiesComplete.js'

const kunmingCenter = [102.712251, 25.040609]
const nearest = findNearestCounties(kunmingCenter, 5)
```

#### `calculateDistance(point1, point2)`
计算两个坐标点之间的距离
```javascript
import { calculateDistance } from './yunnanCountiesComplete.js'

const distance = calculateDistance([102.7, 25.0], [100.2, 25.6])
console.log(distance) // 欧几里得距离
```

### 统计函数

#### `getStatistics()`
获取数据统计信息
```javascript
import { getStatistics } from './yunnanCountiesComplete.js'

const stats = getStatistics()
console.log(stats.totalCities) // 16
console.log(stats.totalCounties) // 129
console.log(stats.averageCountiesPerCity) // 8.06
```

## 使用示例

### 基础使用

```javascript
import {
  yunnanCountiesData,
  getCountiesByCity,
  searchCountiesByName
} from './yunnanCountiesComplete.js'

// 获取昆明市所有区县
const kunming = getCountiesByCity('530100')
console.log(`${kunming.cityName}有${kunming.counties.length}个区县`)

// 搜索自治县
const autonomousCounties = searchCountiesByName('自治县')
console.log(`云南省有${autonomousCounties.length}个自治县`)
```

### 地图应用示例

```javascript
import { findNearestCounties, getCountiesInBounds } from './yunnanCountiesComplete.js'

// 在地图点击事件中查找最近的区县
function onMapClick(event) {
  const clickCoords = [event.lnglat.lng, event.lnglat.lat]
  const nearest = findNearestCounties(clickCoords, 1)
  
  if (nearest.length > 0) {
    const county = nearest[0]
    console.log(`最近的区县：${county.name} (${county.cityName})`)
  }
}

// 获取当前地图视野内的区县
function getVisibleCounties(mapBounds) {
  const bounds = {
    minLng: mapBounds.southwest.lng,
    maxLng: mapBounds.northeast.lng,
    minLat: mapBounds.southwest.lat,
    maxLat: mapBounds.northeast.lat
  }
  
  return getCountiesInBounds(bounds)
}
```

### 完整示例

查看 `yunnanCountiesExample.js` 文件获取更多详细的使用示例。

## 数据来源

- 基础数据来源于项目中的地图JSON文件 (`/src/assets/map/shi/`)
- 部分坐标数据通过网络资源补充和验证
- 所有坐标使用 WGS84 坐标系统

## 注意事项

1. **坐标系统**：所有坐标均为 WGS84 坐标系统（经度，纬度）
2. **距离计算**：`calculateDistance` 函数使用简单的欧几里得距离，适用于小范围计算
3. **数据精度**：坐标精度为小数点后6位，满足大多数应用需求
4. **性能考虑**：对于大量数据查询，建议使用适当的缓存策略

## 更新日志

- **2025-01-30**: 初始版本，包含云南省16个地级行政区的129个区县数据
- 提供完整的查询和统计函数
- 包含详细的使用示例和文档

## 许可证

本数据模块遵循项目的整体许可证协议。
